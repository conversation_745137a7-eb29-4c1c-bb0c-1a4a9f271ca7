[{"C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js": "4", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js": "5", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js": "9", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js": "10", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js": "11", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js": "12", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js": "13", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js": "14", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js": "15", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js": "16", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js": "17", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js": "18", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js": "19", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js": "20", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js": "21", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js": "22", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js": "23", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js": "24", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js": "25", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js": "26"}, {"size": 1991, "mtime": 1749052404537, "results": "27", "hashOfConfig": "28"}, {"size": 3994, "mtime": 1749062321778, "results": "29", "hashOfConfig": "28"}, {"size": 3532, "mtime": 1749052446327, "results": "30", "hashOfConfig": "28"}, {"size": 11609, "mtime": 1749052616010, "results": "31", "hashOfConfig": "28"}, {"size": 9778, "mtime": 1749052716786, "results": "32", "hashOfConfig": "28"}, {"size": 6896, "mtime": 1749052574600, "results": "33", "hashOfConfig": "28"}, {"size": 9470, "mtime": 1749062381799, "results": "34", "hashOfConfig": "28"}, {"size": 13590, "mtime": 1749061640801, "results": "35", "hashOfConfig": "28"}, {"size": 30134, "mtime": 1749062280343, "results": "36", "hashOfConfig": "28"}, {"size": 2649, "mtime": 1749052734801, "results": "37", "hashOfConfig": "28"}, {"size": 2069, "mtime": 1749052794187, "results": "38", "hashOfConfig": "28"}, {"size": 726, "mtime": 1749052818589, "results": "39", "hashOfConfig": "28"}, {"size": 746, "mtime": 1749052802284, "results": "40", "hashOfConfig": "28"}, {"size": 2461, "mtime": 1749052681630, "results": "41", "hashOfConfig": "28"}, {"size": 3308, "mtime": 1749052769149, "results": "42", "hashOfConfig": "28"}, {"size": 737, "mtime": 1749052781889, "results": "43", "hashOfConfig": "28"}, {"size": 4139, "mtime": 1749052837555, "results": "44", "hashOfConfig": "28"}, {"size": 9594, "mtime": 1749061616868, "results": "45", "hashOfConfig": "28"}, {"size": 7101, "mtime": 1749061053794, "results": "46", "hashOfConfig": "28"}, {"size": 18353, "mtime": 1749061024758, "results": "47", "hashOfConfig": "28"}, {"size": 11835, "mtime": 1749061093745, "results": "48", "hashOfConfig": "28"}, {"size": 12912, "mtime": 1749061176127, "results": "49", "hashOfConfig": "28"}, {"size": 7575, "mtime": 1749052484685, "results": "50", "hashOfConfig": "28"}, {"size": 9642, "mtime": 1749062194247, "results": "51", "hashOfConfig": "28"}, {"size": 11300, "mtime": 1749062113585, "results": "52", "hashOfConfig": "28"}, {"size": 13824, "mtime": 1749062158972, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c595us", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js", ["132", "133"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js", ["134"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js", ["135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js", ["162", "163"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js", ["164", "165"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js", ["166", "167"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js", ["168", "169", "170", "171"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js", ["172", "173", "174", "175"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js", ["176", "177", "178", "179", "180", "181"], [], {"ruleId": "182", "severity": 1, "message": "183", "line": 18, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 18, "endColumn": 6}, {"ruleId": "182", "severity": 1, "message": "186", "line": 29, "column": 11, "nodeType": "184", "messageId": "185", "endLine": 29, "endColumn": 19}, {"ruleId": "182", "severity": 1, "message": "187", "line": 42, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 42, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "188", "line": 10, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 10, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "189", "line": 20, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 20, "endColumn": 12}, {"ruleId": "182", "severity": 1, "message": "190", "line": 34, "column": 16, "nodeType": "184", "messageId": "185", "endLine": 34, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "191", "line": 35, "column": 14, "nodeType": "184", "messageId": "185", "endLine": 35, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "192", "line": 36, "column": 15, "nodeType": "184", "messageId": "185", "endLine": 36, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "193", "line": 37, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 37, "endColumn": 17}, {"ruleId": "182", "severity": 1, "message": "194", "line": 38, "column": 12, "nodeType": "184", "messageId": "185", "endLine": 38, "endColumn": 21}, {"ruleId": "182", "severity": 1, "message": "195", "line": 47, "column": 8, "nodeType": "184", "messageId": "185", "endLine": 47, "endColumn": 20}, {"ruleId": "182", "severity": 1, "message": "196", "line": 49, "column": 8, "nodeType": "184", "messageId": "185", "endLine": 49, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "197", "line": 53, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 53, "endColumn": 17}, {"ruleId": "182", "severity": 1, "message": "198", "line": 69, "column": 20, "nodeType": "184", "messageId": "185", "endLine": 69, "endColumn": 31}, {"ruleId": "182", "severity": 1, "message": "199", "line": 70, "column": 29, "nodeType": "184", "messageId": "185", "endLine": 70, "endColumn": 49}, {"ruleId": "182", "severity": 1, "message": "200", "line": 71, "column": 24, "nodeType": "184", "messageId": "185", "endLine": 71, "endColumn": 39}, {"ruleId": "182", "severity": 1, "message": "201", "line": 72, "column": 25, "nodeType": "184", "messageId": "185", "endLine": 72, "endColumn": 41}, {"ruleId": "182", "severity": 1, "message": "202", "line": 78, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 78, "endColumn": 26}, {"ruleId": "182", "severity": 1, "message": "203", "line": 78, "column": 28, "nodeType": "184", "messageId": "185", "endLine": 78, "endColumn": 47}, {"ruleId": "182", "severity": 1, "message": "204", "line": 93, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 93, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "205", "line": 93, "column": 17, "nodeType": "184", "messageId": "185", "endLine": 93, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "206", "line": 94, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 94, "endColumn": 21}, {"ruleId": "182", "severity": 1, "message": "207", "line": 94, "column": 23, "nodeType": "184", "messageId": "185", "endLine": 94, "endColumn": 37}, {"ruleId": "208", "severity": 1, "message": "209", "line": 111, "column": 6, "nodeType": "210", "endLine": 111, "endColumn": 29, "suggestions": "211"}, {"ruleId": "208", "severity": 1, "message": "212", "line": 130, "column": 6, "nodeType": "210", "endLine": 130, "endColumn": 45, "suggestions": "213"}, {"ruleId": "214", "severity": 1, "message": "215", "line": 426, "column": 25, "nodeType": "216", "messageId": "217", "endLine": 426, "endColumn": 72}, {"ruleId": "214", "severity": 1, "message": "215", "line": 433, "column": 25, "nodeType": "216", "messageId": "217", "endLine": 433, "endColumn": 101}, {"ruleId": "214", "severity": 1, "message": "215", "line": 440, "column": 25, "nodeType": "216", "messageId": "217", "endLine": 440, "endColumn": 70}, {"ruleId": "214", "severity": 1, "message": "215", "line": 447, "column": 25, "nodeType": "216", "messageId": "217", "endLine": 447, "endColumn": 58}, {"ruleId": "214", "severity": 1, "message": "215", "line": 454, "column": 25, "nodeType": "216", "messageId": "217", "endLine": 454, "endColumn": 167}, {"ruleId": "182", "severity": 1, "message": "188", "line": 6, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 6, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "218", "line": 7, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 7, "endColumn": 10}, {"ruleId": "182", "severity": 1, "message": "219", "line": 25, "column": 13, "nodeType": "184", "messageId": "185", "endLine": 25, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "220", "line": 83, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 83, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "221", "line": 14, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 14, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "218", "line": 15, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 15, "endColumn": 10}, {"ruleId": "182", "severity": 1, "message": "188", "line": 5, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 5, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "222", "line": 29, "column": 8, "nodeType": "184", "messageId": "185", "endLine": 29, "endColumn": 13}, {"ruleId": "182", "severity": 1, "message": "223", "line": 33, "column": 11, "nodeType": "184", "messageId": "185", "endLine": 33, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "224", "line": 37, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 37, "endColumn": 21}, {"ruleId": "182", "severity": 1, "message": "188", "line": 8, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 8, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "218", "line": 13, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 13, "endColumn": 10}, {"ruleId": "182", "severity": 1, "message": "225", "line": 41, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 41, "endColumn": 21}, {"ruleId": "208", "severity": 1, "message": "226", "line": 61, "column": 6, "nodeType": "210", "endLine": 61, "endColumn": 27, "suggestions": "227"}, {"ruleId": "182", "severity": 1, "message": "218", "line": 22, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 22, "endColumn": 10}, {"ruleId": "182", "severity": 1, "message": "228", "line": 32, "column": 13, "nodeType": "184", "messageId": "185", "endLine": 32, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "192", "line": 33, "column": 15, "nodeType": "184", "messageId": "185", "endLine": 33, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "223", "line": 39, "column": 11, "nodeType": "184", "messageId": "185", "endLine": 39, "endColumn": 15}, {"ruleId": "208", "severity": 1, "message": "229", "line": 52, "column": 6, "nodeType": "210", "endLine": 52, "endColumn": 8, "suggestions": "230"}, {"ruleId": "208", "severity": 1, "message": "231", "line": 59, "column": 6, "nodeType": "210", "endLine": 59, "endColumn": 22, "suggestions": "232"}, "no-unused-vars", "'Fab' is defined but never used.", "Identifier", "unusedVar", "'StopIcon' is defined but never used.", "'recentServers' is assigned a value but never used.", "'Paper' is defined but never used.", "'TextField' is defined but never used.", "'DebugIcon' is defined but never used.", "'RefreshIcon' is defined but never used.", "'MoreIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'FileExplorer' is defined but never used.", "'DeploymentPanel' is defined but never used.", "'navigate' is assigned a value but never used.", "'setBotToken' is assigned a value but never used.", "'setIsConnectedToChat' is assigned a value but never used.", "'setChatServerId' is assigned a value but never used.", "'setChatChannelId' is assigned a value but never used.", "'showDeployDialog' is assigned a value but never used.", "'setShowDeployDialog' is assigned a value but never used.", "'files' is assigned a value but never used.", "'setFiles' is assigned a value but never used.", "'currentFile' is assigned a value but never used.", "'setCurrentFile' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCodeData' and 'loadCodeVersions'. Either include them or remove the dependency array.", "ArrayExpression", ["233"], "React Hook useEffect has a missing dependency: 'handleAutoSave'. Either include it or remove the dependency array.", ["234"], "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "'Divider' is defined but never used.", "'DeleteIcon' is defined but never used.", "'renderCodeDiff' is assigned a value but never used.", "'Alert' is defined but never used.", "'toast' is defined but never used.", "'user' is assigned a value but never used.", "'selectedBot' is assigned a value but never used.", "'onlineUsers' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'connectToChat', 'loadChannelBots', and 'loadMessages'. Either include them or remove the dependency array.", ["235"], "'PersonIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadServers'. Either include it or remove the dependency array.", ["236"], "React Hook useEffect has a missing dependency: 'loadChannels'. Either include it or remove the dependency array.", ["237"], {"desc": "238", "fix": "239"}, {"desc": "240", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "244", "fix": "245"}, {"desc": "246", "fix": "247"}, "Update the dependencies array to be: [applicationId, codeId, loadCodeData, loadCodeVersions]", {"range": "248", "text": "249"}, "Update the dependencies array to be: [code, handleAutoSave, hasUnsavedChanges, originalCode]", {"range": "250", "text": "251"}, "Update the dependencies array to be: [serverId, channelId, connectToChat, loadMessages, loadChannelBots]", {"range": "252", "text": "253"}, "Update the dependencies array to be: [loadServers]", {"range": "254", "text": "255"}, "Update the dependencies array to be: [loadChannels, selectedServer]", {"range": "256", "text": "257"}, [3347, 3370], "[applicationId, codeId, loadCodeData, loadCodeVersions]", [3841, 3880], "[code, handleAutoSave, hasUnsavedChanges, originalCode]", [1558, 1579], "[serverId, channelId, connectToChat, loadMessages, loadChannelBots]", [1382, 1384], "[loadServers]", [1557, 1573], "[loadChannels, selectedServer]"]