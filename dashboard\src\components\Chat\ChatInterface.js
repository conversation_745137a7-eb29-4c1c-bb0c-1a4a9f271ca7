import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Chip,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Send as SendIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { WebSocketService } from '../';
import toast from 'react-hot-toast';

const ChatInterface = ({ serverId, channelId, onBotInvite }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [bots, setBots] = useState([]);
  const [showBotMenu, setShowBotMenu] = useState(false);
  const [botMenuAnchor, setBotMenuAnchor] = useState(null);
  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);
  const [availableBots, setAvailableBots] = useState([]);
  
  const messagesEndRef = useRef(null);
  const wsRef = useRef(null);

  useEffect(() => {
    connectToChat();
    loadMessages();
    loadChannelBots();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, [serverId, channelId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const connectToChat = () => {
    if (wsRef.current) {
      wsRef.current.disconnect();
    }

    wsRef.current = new WebSocketService();
    
    wsRef.current.on('connected', () => {
      setIsConnected(true);
      toast.success('Connected to chat');
    });

    wsRef.current.on('disconnected', () => {
      setIsConnected(false);
      toast.error('Disconnected from chat');
    });

    wsRef.current.on('messageCreate', (message) => {
      setMessages(prev => [...prev, message]);
    });

    wsRef.current.on('userJoin', (userData) => {
      setOnlineUsers(prev => [...prev, userData]);
    });

    wsRef.current.on('userLeave', (userData) => {
      setOnlineUsers(prev => prev.filter(u => u.id !== userData.id));
    });

    wsRef.current.on('botJoin', (botData) => {
      setBots(prev => [...prev, botData]);
      toast.success(`Bot ${botData.name} joined the channel`);
    });

    wsRef.current.connect(user.token);
  };

  const loadMessages = async () => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const loadChannelBots = async () => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setBots(data.bots || []);
      }
    } catch (error) {
      console.error('Error loading channel bots:', error);
    }
  };

  const loadAvailableBots = async () => {
    try {
      const response = await fetch('/api/applications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setAvailableBots(data.applications.filter(app => app.status === 'active'));
      }
    } catch (error) {
      console.error('Error loading available bots:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !isConnected) return;

    try {
      const messageData = {
        content: newMessage,
        channel_id: channelId,
        server_id: serverId
      };

      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(messageData)
      });

      if (response.ok) {
        setNewMessage('');
      } else {
        toast.error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const inviteBot = async (botId) => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ bot_id: botId })
      });

      if (response.ok) {
        toast.success('Bot invited successfully');
        loadChannelBots();
        setShowInviteBotDialog(false);
        
        // Trigger bot invite callback for code editor integration
        if (onBotInvite) {
          const botData = availableBots.find(bot => bot.id === botId);
          onBotInvite(botData);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to invite bot');
      }
    } catch (error) {
      console.error('Error inviting bot:', error);
      toast.error('Failed to invite bot');
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getMessageTime = (timestamp) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const isBot = (author) => {
    return author.bot || bots.some(bot => bot.id === author.id);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header */}
      <Box 
        sx={{ 
          p: 2, 
          borderBottom: 1, 
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h6">
            # general-chat
          </Typography>
          <Chip 
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
        </Box>
        
        <Box display="flex" gap={1}>
          <Button
            startIcon={<AddIcon />}
            onClick={() => {
              loadAvailableBots();
              setShowInviteBotDialog(true);
            }}
            size="small"
          >
            Invite Bot
          </Button>
          
          <IconButton 
            onClick={(e) => {
              setBotMenuAnchor(e.currentTarget);
              setShowBotMenu(true);
            }}
          >
            <MoreIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Messages Area */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        <List>
          {messages.map((message, index) => (
            <ListItem key={message.id || index} alignItems="flex-start">
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: isBot(message.author) ? 'primary.main' : 'secondary.main' }}>
                  {isBot(message.author) ? <BotIcon /> : <PersonIcon />}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {message.author.username}
                    </Typography>
                    {isBot(message.author) && (
                      <Chip label="BOT" size="small" color="primary" />
                    )}
                    <Typography variant="caption" color="text.secondary">
                      {getMessageTime(message.created_at)}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      {/* Message Input */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box display="flex" gap={1}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type a message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={!isConnected}
          />
          <IconButton 
            onClick={sendMessage}
            disabled={!newMessage.trim() || !isConnected}
            color="primary"
          >
            <SendIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Bot Menu */}
      <Menu
        anchorEl={botMenuAnchor}
        open={showBotMenu}
        onClose={() => setShowBotMenu(false)}
      >
        <MenuItem onClick={() => setShowInviteBotDialog(true)}>
          <AddIcon sx={{ mr: 1 }} />
          Invite Bot
        </MenuItem>
        <MenuItem>
          <SettingsIcon sx={{ mr: 1 }} />
          Channel Settings
        </MenuItem>
      </Menu>

      {/* Invite Bot Dialog */}
      <Dialog 
        open={showInviteBotDialog} 
        onClose={() => setShowInviteBotDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Invite Bot to Channel</DialogTitle>
        <DialogContent>
          <List>
            {availableBots.map((bot) => (
              <ListItem key={bot.id}>
                <ListItemAvatar>
                  <Avatar>
                    <BotIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={bot.name}
                  secondary={bot.description || 'No description'}
                />
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => inviteBot(bot.id)}
                  disabled={bots.some(b => b.id === bot.id)}
                >
                  {bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'}
                </Button>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInviteBotDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChatInterface;
