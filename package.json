{"name": "bot-fake-discord", "version": "1.0.0", "description": "A Discord-like bot platform with SQLite3", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "migrate": "node src/database/migrate.js", "test": "jest", "test-api": "node test-api.js", "test-websocket": "node test-websocket.js", "test-runtime": "node test-runtime.js", "example-bot": "node examples/simple-bot.js", "advanced-bot": "node examples/advanced-bot.js", "demo": "node demo.js", "dashboard:install": "cd dashboard && npm install", "dashboard:start": "cd dashboard && npm start", "dashboard:build": "cd dashboard && npm run build", "cluster": "node cluster.js", "cluster:dev": "cross-env NODE_ENV=development node cluster.js", "cluster:prod": "cross-env NODE_ENV=production node cluster.js", "optimize-db": "node -e \"require('./src/database/performance').applyOptimizations()\"", "test-production": "npm run test-api && npm run test-websocket && npm run test-runtime && node tests/production.test.js"}, "keywords": ["bot", "discord", "platform", "websocket"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "prom-client": "^15.1.0", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}