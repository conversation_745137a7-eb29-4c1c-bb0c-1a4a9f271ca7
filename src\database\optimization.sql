-- Database Optimization Script
-- Performance indexes and optimizations for production

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Applications table indexes
CREATE INDEX IF NOT EXISTS idx_applications_owner_id ON applications(owner_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);
CREATE INDEX IF NOT EXISTS idx_applications_owner_status ON applications(owner_id, status);

-- Servers table indexes
CREATE INDEX IF NOT EXISTS idx_servers_owner_id ON servers(owner_id);
CREATE INDEX IF NOT EXISTS idx_servers_created_at ON servers(created_at);

-- Channels table indexes
CREATE INDEX IF NOT EXISTS idx_channels_server_id ON channels(server_id);
CREATE INDEX IF NOT EXISTS idx_channels_type ON channels(type);
CREATE INDEX IF NOT EXISTS idx_channels_server_type ON channels(server_id, type);

-- Bot instances table indexes
CREATE INDEX IF NOT EXISTS idx_bot_instances_application_id ON bot_instances(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_instances_server_id ON bot_instances(server_id);
CREATE INDEX IF NOT EXISTS idx_bot_instances_status ON bot_instances(status);
CREATE INDEX IF NOT EXISTS idx_bot_instances_created_at ON bot_instances(created_at);
CREATE INDEX IF NOT EXISTS idx_bot_instances_app_server ON bot_instances(application_id, server_id);

-- Messages table indexes
CREATE INDEX IF NOT EXISTS idx_messages_channel_id ON messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_messages_author_id ON messages(author_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_channel_created ON messages(channel_id, created_at);

-- Bot code table indexes
CREATE INDEX IF NOT EXISTS idx_bot_code_application_id ON bot_code(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_code_version ON bot_code(version);
CREATE INDEX IF NOT EXISTS idx_bot_code_status ON bot_code(status);
CREATE INDEX IF NOT EXISTS idx_bot_code_created_at ON bot_code(created_at);
CREATE INDEX IF NOT EXISTS idx_bot_code_app_version ON bot_code(application_id, version);

-- Bot runtime instances table indexes
CREATE INDEX IF NOT EXISTS idx_bot_runtime_application_id ON bot_runtime_instances(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_runtime_status ON bot_runtime_instances(status);
CREATE INDEX IF NOT EXISTS idx_bot_runtime_created_at ON bot_runtime_instances(created_at);

-- Bot logs table indexes
CREATE INDEX IF NOT EXISTS idx_bot_logs_application_id ON bot_logs(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_logs_level ON bot_logs(level);
CREATE INDEX IF NOT EXISTS idx_bot_logs_created_at ON bot_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_bot_logs_app_level ON bot_logs(application_id, level);
CREATE INDEX IF NOT EXISTS idx_bot_logs_app_created ON bot_logs(application_id, created_at);

-- Bot storage table indexes
CREATE INDEX IF NOT EXISTS idx_bot_storage_application_id ON bot_storage(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_storage_key ON bot_storage(key);
CREATE INDEX IF NOT EXISTS idx_bot_storage_server_id ON bot_storage(server_id);
CREATE INDEX IF NOT EXISTS idx_bot_storage_expires_at ON bot_storage(expires_at);
CREATE INDEX IF NOT EXISTS idx_bot_storage_app_key ON bot_storage(application_id, key);
CREATE INDEX IF NOT EXISTS idx_bot_storage_app_server ON bot_storage(application_id, server_id);

-- Bot scheduled tasks table indexes
CREATE INDEX IF NOT EXISTS idx_bot_tasks_application_id ON bot_scheduled_tasks(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_tasks_status ON bot_scheduled_tasks(status);
CREATE INDEX IF NOT EXISTS idx_bot_tasks_next_run ON bot_scheduled_tasks(next_run);
CREATE INDEX IF NOT EXISTS idx_bot_tasks_app_status ON bot_scheduled_tasks(application_id, status);

-- Rate limits table indexes
CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limits_timestamp ON rate_limits(timestamp);
CREATE INDEX IF NOT EXISTS idx_rate_limits_id_timestamp ON rate_limits(identifier, timestamp);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- For dashboard queries
CREATE INDEX IF NOT EXISTS idx_applications_owner_status_created ON applications(owner_id, status, created_at);
CREATE INDEX IF NOT EXISTS idx_bot_instances_app_status_created ON bot_instances(application_id, status, created_at);
CREATE INDEX IF NOT EXISTS idx_messages_channel_author_created ON messages(channel_id, author_id, created_at);

-- For analytics queries
CREATE INDEX IF NOT EXISTS idx_bot_logs_app_level_created ON bot_logs(application_id, level, created_at);
CREATE INDEX IF NOT EXISTS idx_bot_storage_app_server_key ON bot_storage(application_id, server_id, key);

-- ============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- ============================================================================

-- Active applications only
CREATE INDEX IF NOT EXISTS idx_applications_active ON applications(owner_id, created_at) 
WHERE status = 'active';

-- Running bot instances only
CREATE INDEX IF NOT EXISTS idx_bot_instances_running ON bot_instances(application_id, created_at) 
WHERE status = 'running';

-- Non-expired storage entries
CREATE INDEX IF NOT EXISTS idx_bot_storage_active ON bot_storage(application_id, key) 
WHERE expires_at IS NULL OR expires_at > datetime('now');

-- Pending scheduled tasks
CREATE INDEX IF NOT EXISTS idx_bot_tasks_pending ON bot_scheduled_tasks(next_run) 
WHERE status = 'pending';

-- Recent error logs (last 7 days)
CREATE INDEX IF NOT EXISTS idx_bot_logs_recent_errors ON bot_logs(application_id, created_at) 
WHERE level = 'error' AND created_at > datetime('now', '-7 days');

-- ============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- ============================================================================

-- Enable Write-Ahead Logging for better concurrency
PRAGMA journal_mode = WAL;

-- Increase cache size (in pages, default is 2000)
PRAGMA cache_size = 10000;

-- Optimize for faster writes
PRAGMA synchronous = NORMAL;

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Optimize temp store
PRAGMA temp_store = MEMORY;

-- Set page size for better performance (must be set before any tables are created)
-- PRAGMA page_size = 4096;

-- ============================================================================
-- MAINTENANCE QUERIES
-- ============================================================================

-- Analyze tables for query optimization
ANALYZE;

-- Update table statistics
UPDATE sqlite_stat1 SET stat = NULL;
ANALYZE;

-- ============================================================================
-- CLEANUP PROCEDURES
-- ============================================================================

-- Clean up expired storage entries
DELETE FROM bot_storage 
WHERE expires_at IS NOT NULL AND expires_at < datetime('now');

-- Clean up old rate limit entries (older than 1 hour)
DELETE FROM rate_limits 
WHERE timestamp < datetime('now', '-1 hour');

-- Clean up old logs (older than 30 days)
DELETE FROM bot_logs 
WHERE created_at < datetime('now', '-30 days');

-- Clean up completed scheduled tasks (older than 7 days)
DELETE FROM bot_scheduled_tasks 
WHERE status = 'completed' AND updated_at < datetime('now', '-7 days');

-- ============================================================================
-- VACUUM AND OPTIMIZE
-- ============================================================================

-- Reclaim unused space and defragment
VACUUM;

-- Rebuild indexes
REINDEX;

-- ============================================================================
-- MONITORING QUERIES
-- ============================================================================

-- Check index usage
-- SELECT name, tbl_name FROM sqlite_master WHERE type = 'index';

-- Check table sizes
-- SELECT name, 
--        (SELECT COUNT(*) FROM sqlite_master WHERE type = 'table' AND name = m.name) as table_count,
--        (SELECT COUNT(*) FROM sqlite_master WHERE type = 'index' AND tbl_name = m.name) as index_count
-- FROM sqlite_master m WHERE type = 'table';

-- Check database size
-- SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size();

-- ============================================================================
-- PERFORMANCE VIEWS
-- ============================================================================

-- View for application statistics
CREATE VIEW IF NOT EXISTS v_application_stats AS
SELECT 
    a.id,
    a.name,
    a.owner_id,
    a.status,
    COUNT(DISTINCT bi.id) as instance_count,
    COUNT(DISTINCT bc.id) as code_versions,
    COUNT(DISTINCT bl.id) as log_count,
    MAX(bl.created_at) as last_activity
FROM applications a
LEFT JOIN bot_instances bi ON a.id = bi.application_id
LEFT JOIN bot_code bc ON a.id = bc.application_id
LEFT JOIN bot_logs bl ON a.id = bl.application_id
GROUP BY a.id, a.name, a.owner_id, a.status;

-- View for server statistics
CREATE VIEW IF NOT EXISTS v_server_stats AS
SELECT 
    s.id,
    s.name,
    s.owner_id,
    COUNT(DISTINCT c.id) as channel_count,
    COUNT(DISTINCT bi.id) as bot_count,
    COUNT(DISTINCT m.id) as message_count
FROM servers s
LEFT JOIN channels c ON s.id = c.server_id
LEFT JOIN bot_instances bi ON s.id = bi.server_id
LEFT JOIN messages m ON c.id = m.channel_id
GROUP BY s.id, s.name, s.owner_id;

-- View for bot performance metrics
CREATE VIEW IF NOT EXISTS v_bot_performance AS
SELECT 
    a.id as application_id,
    a.name as application_name,
    COUNT(CASE WHEN bl.level = 'error' THEN 1 END) as error_count,
    COUNT(CASE WHEN bl.level = 'warn' THEN 1 END) as warning_count,
    COUNT(CASE WHEN bl.level = 'info' THEN 1 END) as info_count,
    COUNT(bl.id) as total_logs,
    AVG(CASE WHEN bl.level = 'error' THEN 1 ELSE 0 END) as error_rate
FROM applications a
LEFT JOIN bot_logs bl ON a.id = bl.application_id 
    AND bl.created_at > datetime('now', '-24 hours')
GROUP BY a.id, a.name;
