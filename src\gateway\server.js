const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const BotConnection = require('./connection');
const { GATEWAY_EVENTS, createGatewayPayload } = require('./events');

/**
 * WebSocket Gateway Server for bot connections
 */
class GatewayServer {
    constructor(options = {}) {
        this.port = options.port || process.env.WS_PORT || 3005;
        this.server = null;
        this.wss = null;
        this.connections = new Map(); // Map of connection ID to BotConnection
        this.botConnections = new Map(); // Map of bot application ID to BotConnection
        this.eventHandlers = new Map();
        
        this.setupEventHandlers();
    }

    /**
     * Setup default event handlers
     */
    setupEventHandlers() {
        // Register default event handlers
        this.on('connection', this.handleConnection.bind(this));
        this.on('bot_authenticated', this.handleBotAuthenticated.bind(this));
        this.on('bot_disconnected', this.handleBotDisconnected.bind(this));
    }

    /**
     * Start the WebSocket server
     */
    async start() {
        try {
            // Create HTTP server for WebSocket upgrade
            this.server = http.createServer();
            
            // Create WebSocket server
            this.wss = new WebSocket.Server({
                server: this.server,
                path: '/gateway',
                verifyClient: this.verifyClient.bind(this)
            });

            // Handle WebSocket connections
            this.wss.on('connection', (ws, req) => {
                const connection = new BotConnection(ws, this);
                this.emit('connection', connection, req);
            });

            // Start HTTP server
            this.server.listen(this.port, () => {
                console.log(`WebSocket Gateway server running on port ${this.port}`);
                console.log(`Gateway URL: ws://localhost:${this.port}/gateway`);
            });

            // Handle server errors
            this.server.on('error', (error) => {
                console.error('Gateway server error:', error);
            });

        } catch (error) {
            console.error('Failed to start Gateway server:', error);
            throw error;
        }
    }

    /**
     * Stop the WebSocket server
     */
    async stop() {
        try {
            // Close all connections
            for (const connection of this.connections.values()) {
                connection.close(1001, 'Server shutting down');
            }

            // Close WebSocket server
            if (this.wss) {
                this.wss.close();
            }

            // Close HTTP server
            if (this.server) {
                this.server.close();
            }

            console.log('Gateway server stopped');
        } catch (error) {
            console.error('Error stopping Gateway server:', error);
        }
    }

    /**
     * Verify client connection (optional authentication/filtering)
     * @param {Object} info - Connection info
     * @returns {boolean} - True to allow connection
     */
    verifyClient(info) {
        // Basic verification - can be extended
        const { origin, secure, req } = info;
        
        // Log connection attempt
        console.log(`Gateway connection attempt from ${req.socket.remoteAddress}`);
        
        // Allow all connections for now
        return true;
    }

    /**
     * Handle new WebSocket connection
     * @param {BotConnection} connection - New bot connection
     * @param {Object} req - HTTP request object
     */
    handleConnection(connection, req) {
        console.log(`New gateway connection: ${connection.id}`);
        
        // Store connection
        this.connections.set(connection.id, connection);
        
        // Setup connection event handlers
        connection.ws.on('close', () => {
            this.connections.delete(connection.id);
            if (connection.bot) {
                this.botConnections.delete(connection.bot.applicationId);
                this.emit('bot_disconnected', connection.bot);
            }
        });
    }

    /**
     * Handle bot authentication
     * @param {Object} bot - Authenticated bot info
     * @param {BotConnection} connection - Bot connection
     */
    handleBotAuthenticated(bot, connection) {
        console.log(`Bot authenticated: ${bot.name} (${bot.applicationId})`);
        
        // Store bot connection
        this.botConnections.set(bot.applicationId, connection);
        
        // Emit bot online event to other systems
        this.broadcastBotStatusChange(bot.applicationId, 'online');
    }

    /**
     * Handle bot disconnection
     * @param {Object} bot - Disconnected bot info
     */
    handleBotDisconnected(bot) {
        console.log(`Bot disconnected: ${bot.name} (${bot.applicationId})`);
        
        // Remove bot connection
        this.botConnections.delete(bot.applicationId);
        
        // Emit bot offline event to other systems
        this.broadcastBotStatusChange(bot.applicationId, 'offline');
    }

    /**
     * Add a connection to the gateway
     * @param {BotConnection} connection - Bot connection
     */
    addConnection(connection) {
        if (connection.bot) {
            this.botConnections.set(connection.bot.applicationId, connection);
            this.emit('bot_authenticated', connection.bot, connection);
        }
    }

    /**
     * Remove a connection from the gateway
     * @param {BotConnection} connection - Bot connection
     */
    removeConnection(connection) {
        if (connection.bot) {
            this.botConnections.delete(connection.bot.applicationId);
            this.emit('bot_disconnected', connection.bot);
        }
    }

    /**
     * Broadcast event to all connected bots
     * @param {string} event - Event type
     * @param {Object} data - Event data
     * @param {Array} excludeBots - Bot IDs to exclude from broadcast
     */
    broadcastToAllBots(event, data, excludeBots = []) {
        const payload = createGatewayPayload(event, data);
        
        for (const [botId, connection] of this.botConnections) {
            if (!excludeBots.includes(botId)) {
                connection.send(payload);
            }
        }
    }

    /**
     * Send event to specific bot
     * @param {string} botApplicationId - Bot application ID
     * @param {string} event - Event type
     * @param {Object} data - Event data
     * @returns {boolean} - True if sent successfully
     */
    sendToBotById(botApplicationId, event, data) {
        const connection = this.botConnections.get(botApplicationId);
        if (connection) {
            const payload = createGatewayPayload(event, data, connection.sequence++);
            connection.send(payload);
            return true;
        }
        return false;
    }

    /**
     * Send event to bots in a specific server
     * @param {string} serverId - Server ID
     * @param {string} event - Event type
     * @param {Object} data - Event data
     */
    async sendToBotsInServer(serverId, event, data) {
        try {
            const database = require('../database/connection');
            
            // Get all bot instances in the server
            const instances = await database.all(
                'SELECT application_id FROM bot_instances WHERE server_id = ? AND status = ?',
                [serverId, 'online']
            );

            // Send event to each bot
            for (const instance of instances) {
                this.sendToBotById(instance.application_id, event, data);
            }
        } catch (error) {
            console.error('Error sending event to bots in server:', error);
        }
    }

    /**
     * Broadcast bot status change
     * @param {string} botApplicationId - Bot application ID
     * @param {string} status - New status
     */
    broadcastBotStatusChange(botApplicationId, status) {
        const data = {
            bot_id: botApplicationId,
            status: status,
            timestamp: new Date().toISOString()
        };

        this.broadcastToAllBots(GATEWAY_EVENTS.BOT_INSTANCE_UPDATE, data, [botApplicationId]);
    }

    /**
     * Get gateway statistics
     * @returns {Object} - Gateway stats
     */
    getStats() {
        return {
            total_connections: this.connections.size,
            authenticated_bots: this.botConnections.size,
            uptime: process.uptime(),
            memory_usage: process.memoryUsage()
        };
    }

    /**
     * Get list of connected bots
     * @returns {Array} - Array of connected bot info
     */
    getConnectedBots() {
        const bots = [];
        for (const [botId, connection] of this.botConnections) {
            bots.push({
                application_id: botId,
                name: connection.bot.name,
                session_id: connection.sessionId,
                connected_at: connection.connectedAt,
                last_heartbeat: connection.lastHeartbeat
            });
        }
        return bots;
    }

    /**
     * Register event handler
     * @param {string} event - Event name
     * @param {Function} handler - Event handler function
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Emit event to registered handlers
     * @param {string} event - Event name
     * @param {...any} args - Event arguments
     */
    emit(event, ...args) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            for (const handler of handlers) {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            }
        }
    }
}

module.exports = GatewayServer;
